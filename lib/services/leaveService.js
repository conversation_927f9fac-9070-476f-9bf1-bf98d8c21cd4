/**
 * Service xử lý logic xin nghỉ phép
 * Quản lý tạo đơn, duyệt đơn xin nghỉ phép/đi muộn
 */

const _ = require('lodash');
const moment = require('moment');
const LeaveRequest = require('../models/leaveRequest');
const User = require('../models/user');
const attendancePermission = require('../util/attendancePermission');
const NotificationHelper = require('../util/notificationHelper');
const DateUtils = require('../utils/dateUtils');

class LeaveService {
  /**
   * Tạo đơn xin nghỉ/đi muộn
   * @param {String} userId - ID cán bộ
   * @param {Object} requestData - Dữ liệu đơn xin nghỉ
   * @returns {Object} Kết quả tạo đơn
   */
  async createLeaveRequest(userId, requestData) {
    try {
      const {
        type,
        startDate,
        endDate,
        shift,
        reason,
        attachments = []
      } = requestData;

      // Validate dữ liệu theo loại đơn
      const validation = this.validateLeaveRequest(type, {
        startDate,
        endDate,
        shift,
        reason
      });

      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Dữ liệu không hợp lệ',
            body: validation.message
          },
          data: null
        };
      }

      // Tính số ngày nghỉ
      const dayCount = this.calculateDayCount(type, DateUtils.convertDDMMYYYYtoYYYYMMDD(startDate), DateUtils.convertDDMMYYYYtoYYYYMMDD(endDate), shift);

      // Tạo đơn xin nghỉ
      const leaveRequestData = {
        user: userId,
        type,
        startDate,
        endDate: type === 'leave' ? endDate : startDate,
        shift,
        dayCount,
        reason,
        attachments,
        status: 'pending'
      };

      const leaveRequest = await LeaveRequest.create(leaveRequestData);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Tạo đơn xin nghỉ thành công'
        },
        data: leaveRequest
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Duyệt đơn xin nghỉ
   * @param {String} requestId - ID đơn xin nghỉ
   * @param {String} approverId - ID người duyệt
   * @param {String} status - Trạng thái duyệt ('approved' | 'rejected')
   * @param {String} approvalNote - Ghi chú duyệt (optional)
   * @returns {Object} Kết quả duyệt đơn
   */
  async approveLeaveRequest(requestId, approverId, status, approvalNote = '') {
    try {
      // Kiểm tra đơn xin nghỉ
      const leaveRequest = await LeaveRequest.findById(requestId)
        .populate('user', 'name idNumber units');

      if (!leaveRequest) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy đơn xin nghỉ'
          },
          data: null
        };
      }

      if (leaveRequest.status !== 'pending') {
        return {
          success: false,
          message: {
            head: 'Không thể xử lý',
            body: 'Đơn xin nghỉ đã được xử lý'
          },
          data: null
        };
      }

      // Kiểm tra quyền duyệt (có thể duyệt cho cán bộ cùng đơn vị hoặc cấp dưới)
      const permissionCheck = await attendancePermission.checkStatisticsPermission(
        approverId,
        leaveRequest.user._id.toString()
      );

      if (!permissionCheck.canView) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: 'Không có quyền duyệt đơn xin nghỉ này'
          },
          data: null
        };
      }

      // Cập nhật trạng thái đơn
      leaveRequest.status = status;
      leaveRequest.approvedBy = approverId;
      leaveRequest.approvedAt = Date.now();
      leaveRequest.approvalNote = approvalNote;
      leaveRequest.updatedAt = Date.now();

      await leaveRequest.save();

      // Gửi thông báo khi đơn được duyệt
      NotificationHelper.notifyLeaveRequestApproval(leaveRequest);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: status === 'approved' ? 'Duyệt đơn xin nghỉ thành công' : 'Từ chối đơn xin nghỉ'
        },
        data: leaveRequest
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy danh sách đơn xin nghỉ
   * @param {String} userId - ID người xem (optional)
   * @param {Object} filters - Bộ lọc
   * @returns {Object} Danh sách đơn xin nghỉ
   */
  async getLeaveRequests(userId, filters = {}) {
    try {
      const {
        requesterId, // ID người tạo đơn (để admin xem đơn của cán bộ khác)
        status,
        type,
        startDate,
        endDate,
        page = 1,
        limit = 20
      } = filters;

      let query = {};

      // Nếu có requesterId thì kiểm tra quyền xem
      if (requesterId) {
        const permissionCheck = await attendancePermission.checkStatisticsPermission(
          userId,
          requesterId
        );

        if (!permissionCheck.canView) {
          return {
            success: false,
            message: {
              head: 'Không có quyền',
              body: 'Không có quyền xem đơn xin nghỉ của cán bộ này'
            },
            data: null
          };
        }

        query.user = requesterId;
      } else {
        // Nếu không có requesterId thì xem đơn của chính mình
        query.user = userId;
      }

      // Áp dụng filters
      if (status) {
        query.status = status;
      }

      if (type) {
        query.type = type;
      }

      if (startDate && endDate) {
        query.startDate = {
          $gte: startDate,
          $lte: endDate
        };
      }

      const skip = (page - 1) * limit;

      const [requests, total] = await Promise.all([
        LeaveRequest.find(query)
          .populate('user', 'name idNumber units')
          .populate('approvedBy', 'name')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        LeaveRequest.countDocuments(query)
      ]);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách đơn xin nghỉ thành công'
        },
        data: {
          requests,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy chi tiết đơn xin nghỉ
   * @param {String} requestId - ID đơn xin nghỉ
   * @param {String} userId - ID người xem
   * @returns {Object} Chi tiết đơn xin nghỉ
   */
  async getLeaveRequestDetail(requestId, userId) {
    try {
      const leaveRequest = await LeaveRequest.findById(requestId)
        .populate('user', 'name idNumber units')
        .populate('approvedBy', 'name')
        .lean();

      if (!leaveRequest) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy đơn xin nghỉ'
          },
          data: null
        };
      }

      // Kiểm tra quyền xem
      const canView = leaveRequest.user._id.toString() === userId ||
        (await attendancePermission.checkStatisticsPermission(
          userId,
          leaveRequest.user._id.toString()
        )).canView;

      if (!canView) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: 'Không có quyền xem đơn xin nghỉ này'
          },
          data: null
        };
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy chi tiết đơn xin nghỉ thành công'
        },
        data: leaveRequest
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Validate dữ liệu đơn xin nghỉ
   * @param {String} type - Loại đơn
   * @param {Object} data - Dữ liệu đơn
   * @returns {Object} Kết quả validate
   */
  validateLeaveRequest(type, data) {
    const { startDate, endDate, shift, reason } = data;

    if (!reason || !reason.trim()) {
      return {
        isValid: false,
        message: 'Phải nhập lý do xin nghỉ'
      };
    }

    if (!startDate) {
      return {
        isValid: false,
        message: 'Phải chọn ngày bắt đầu'
      };
    }

    // Validate theo loại đơn
    switch (type) {
      case 'leave':
        if (!endDate) {
          return {
            isValid: false,
            message: 'Phải chọn ngày kết thúc cho đơn xin nghỉ phép'
          };
        }
        if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
          return {
            isValid: false,
            message: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc'
          };
        }
        break;

      case 'late_arrival':
      case 'emergency_leave':
        if (!shift) {
          return {
            isValid: false,
            message: 'Phải chọn ca làm việc'
          };
        }
        if (!['morning', 'afternoon', 'both'].includes(shift)) {
          return {
            isValid: false,
            message: 'Ca làm việc không hợp lệ'
          };
        }
        break;

      default:
        return {
          isValid: false,
          message: 'Loại đơn xin nghỉ không hợp lệ'
        };
    }

    return {
      isValid: true,
      message: 'Dữ liệu hợp lệ'
    };
  }

  /**
   * Tính số ngày nghỉ
   * @param {String} type - Loại đơn
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @param {String} shift - Ca làm việc
   * @returns {Number} Số ngày nghỉ
   */
  calculateDayCount(type, startDate, endDate, shift) {
    switch (type) {
      case 'leave':
        const start = moment(startDate);
        const end = moment(endDate);
        return end.diff(start, 'days') + 1;

      // case 'late_arrival':
      case 'emergency_leave':
        if (shift === 'both') {
          return 1;
        } else {
          return 0.5; // Nửa ngày
        }

      default:
        return 0;
    }
  }

  /**
   * Lấy thống kê đơn xin nghỉ
   * @param {String} userId - ID người xem
   * @param {Object} filters - Bộ lọc
   * @returns {Object} Thống kê đơn xin nghỉ
   */
  async getLeaveStatistics(userId, filters = {}) {
    try {
      const {
        targetUserId,
        startDate,
        endDate
      } = filters;

      // Kiểm tra quyền xem thống kê
      let query = {};

      if (targetUserId) {
        const permissionCheck = await attendancePermission.checkStatisticsPermission(
          userId,
          targetUserId
        );

        if (!permissionCheck.canView) {
          return {
            success: false,
            message: {
              head: 'Không có quyền',
              body: 'Không có quyền xem thống kê của cán bộ này'
            },
            data: null
          };
        }

        query.user = targetUserId;
      } else {
        query.user = userId;
      }

      // Áp dụng filter thời gian
      if (startDate && endDate) {
        query.startDate = {
          $gte: startDate,
          $lte: endDate
        };
      }

      // Thống kê theo trạng thái
      const [statusStats, typeStats, totalRequests] = await Promise.all([
        LeaveRequest.aggregate([
          { $match: query },
          { $group: { _id: '$status', count: { $sum: 1 } } }
        ]),
        LeaveRequest.aggregate([
          { $match: query },
          { $group: { _id: '$type', count: { $sum: 1 }, totalDays: { $sum: '$dayCount' } } }
        ]),
        LeaveRequest.countDocuments(query)
      ]);

      const statistics = {
        total: totalRequests,
        byStatus: this.formatAggregationResult(statusStats),
        byType: this.formatAggregationResult(typeStats, true),
        period: { startDate, endDate }
      };

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê đơn xin nghỉ thành công'
        },
        data: statistics
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Format kết quả aggregation
   * @param {Array} results - Kết quả aggregation
   * @param {Boolean} includeDays - Có bao gồm tổng ngày không
   * @returns {Object} Kết quả đã format
   */
  formatAggregationResult(results, includeDays = false) {
    const formatted = {};

    results.forEach(item => {
      formatted[item._id] = {
        count: item.count
      };

      if (includeDays && item.totalDays !== undefined) {
        formatted[item._id].totalDays = item.totalDays;
      }
    });

    return formatted;
  }
}

module.exports = new LeaveService();