const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyPatrolScheduleModel = require('../../../models/dutyPatrolSchedule');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, templateId, unit, requiredOfficer } = req.body; // _id: ID của schedule, templateId: ID của phần tử trong weeklyScheduleTemplate, unit: ID của unit cần cập nhật, requiredOfficer: số lượng cán bộ mới

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch tuần tra'
        }
      });
    }

    if (!templateId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID của phần tử template'
        }
      });
    }

    if (!unit) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID của unit'
        }
      });
    }

    if (typeof requiredOfficer !== 'number' || requiredOfficer < 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'requiredOfficer phải là số >= 0'
        }
      });
    }

    next();
  };

  const validateMaxOfficer = (next) => {
    // Đếm số lượng user thuộc unit
    UserModel.countDocuments({
      units: new mongoose.Types.ObjectId(unit),
      status: 1
    }, (err, maxOfficer) => {
      if (err) {
        return next(err);
      }

      if (requiredOfficer > maxOfficer) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Số lượng cán bộ yêu cầu (${requiredOfficer}) không thể vượt quá số lượng cán bộ tối đa của đơn vị (${maxOfficer})`
          }
        });
      }

      next();
    });
  };



  const updateWeeklyScheduleTemplate = (next) => {
    // Lấy dữ liệu hiện tại
    DutyPatrolScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'lịch tuần tra không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tạo bản sao của weeklyScheduleTemplate hiện tại
        let updatedTemplate = [...(schedule.weeklyScheduleTemplate || [])];

        // Tìm và cập nhật phần tử có templateId khớp
        const index = updatedTemplate.findIndex(item => item._id.toString() === templateId);
        if (index === -1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy phần tử template với ID đã cung cấp'
            }
          });
        }

        // Tìm và cập nhật phần tử có unit khớp trong data của template
        const currentItem = updatedTemplate[index];
        const updatedData = currentItem.data.map(shift => {
          // So sánh unit ID (xử lý cả trường hợp unit là object hoặc string)
          const shiftUnitId = shift.unit && shift.unit._id ? shift.unit._id.toString() : (shift.unit ? shift.unit.toString() : null);
          const targetUnitId = unit.toString();
          
          if (shiftUnitId === targetUnitId) {
            return {
              ...shift,
              requiredOfficer: requiredOfficer
            };
          }
          return shift;
        });

        // Kiểm tra xem có tìm thấy unit để cập nhật không
        const foundUnit = currentItem.data.some(shift => {
          const shiftUnitId = shift.unit && shift.unit._id ? shift.unit._id.toString() : (shift.unit ? shift.unit.toString() : null);
          return shiftUnitId === unit.toString();
        });

        if (!foundUnit) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy unit trong template để cập nhật'
            }
          });
        }

        updatedTemplate[index] = {
          ...currentItem,
          data: updatedData
        };

        // Cập nhật vào database
        DutyPatrolScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: updatedTemplate,
              updatedAt: Date.now()
            }
          },
          { new: true }
        )
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật lịch tuần tra thành công'
            },
            data: updatedSchedule
          });
        });
      });
  };

  async.waterfall([checkParams, validateMaxOfficer, updateWeeklyScheduleTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
