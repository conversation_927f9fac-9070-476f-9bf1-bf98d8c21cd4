const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyLocationScheduleModel = require('../../../models/dutyLocationSchedule');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, weeklySchedule } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON>ui lòng cung cấp ID lịch thường trực chiến đấu'
        }
      });
    }

    // Validate weeklySchedule
    if (!weeklySchedule || !Array.isArray(weeklySchedule)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'weeklySchedule phải là mảng các ID ca trực'
        }
      });
    }

    next();
  };

  const validateTimeConflicts = (next) => {
    // Lấy thông tin chi tiết của tất cả ca trực trong weeklySchedule
    DutyShiftModel.find({
      _id: { $in: weeklySchedule }
    })
    .populate('officer', 'name')
    .select('_id officer startTime endTime name')
    .lean()
    .exec((err, shifts) => {
      if (err) {
        return next(err);
      }

      // Kiểm tra tất cả ca trực có tồn tại không
      if (shifts.length !== weeklySchedule.length) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Một số ca trực không tồn tại trong hệ thống'
          }
        });
      }

      // Helper function để format thời gian
      const formatTime = (timestamp) => {
        const date = new Date(timestamp);
        const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
        const dayName = days[date.getDay()];
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${dayName} ${hours}:${minutes}`;
      };

      // Nhóm ca trực theo officer
      const shiftsByOfficer = {};
      shifts.forEach(shift => {
        const officerId = shift.officer._id.toString();
        if (!shiftsByOfficer[officerId]) {
          shiftsByOfficer[officerId] = {
            officerName: shift.officer.name,
            shifts: []
          };
        }
        shiftsByOfficer[officerId].shifts.push(shift);
      });

      // Kiểm tra trùng lặp thời gian cho từng cán bộ
      for (const officerId in shiftsByOfficer) {
        const officerData = shiftsByOfficer[officerId];
        const officerShifts = officerData.shifts;
        
        // Nếu cán bộ có hơn 1 ca trực, kiểm tra trùng lặp
        if (officerShifts.length > 1) {
          for (let i = 0; i < officerShifts.length; i++) {
            for (let j = i + 1; j < officerShifts.length; j++) {
              const shift1 = officerShifts[i];
              const shift2 = officerShifts[j];

              // Kiểm tra trùng lặp thời gian
              const isConflict = (
                (shift1.startTime < shift2.endTime && shift1.endTime > shift2.startTime) ||
                (shift2.startTime < shift1.endTime && shift2.endTime > shift1.startTime)
              );

              if (isConflict) {
                return next({
                  code: CONSTANTS.CODE.WRONG_PARAMS,
                  message: {
                    head: 'Thông báo',
                    body: `Cán bộ ${officerData.officerName} có ca trực trùng lặp thời gian: "${shift1.name}" (${formatTime(shift1.startTime)} - ${formatTime(shift1.endTime)}) và "${shift2.name}" (${formatTime(shift2.startTime)} - ${formatTime(shift2.endTime)})`
                  }
                });
              }
            }
          }
        }
      }

      next();
    });
  };

  const updateWeeklySchedule = (next) => {
    // Lấy dữ liệu hiện tại để so sánh
    DutyLocationScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, currentSchedule) => {
        if (err) {
          return next(err);
        }

        if (!currentSchedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch thường trực chiến đấu không tồn tại hoặc đã bị xóa'
            }
          });
        }

        const oldWeeklySchedule = currentSchedule.weeklySchedule || [];
        const newWeeklySchedule = weeklySchedule || [];

        // Tìm các phần tử được thêm và xóa
        const addedShifts = newWeeklySchedule.filter(id => 
          !oldWeeklySchedule.some(oldId => oldId.toString() === id.toString())
        );
        const removedShifts = oldWeeklySchedule.filter(oldId => 
          !newWeeklySchedule.some(id => id.toString() === oldId.toString())
        );

        // Cập nhật weeklySchedule trong dutyLocationSchedule
        DutyLocationScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklySchedule: weeklySchedule,
              updatedAt: Date.now()
            }
          },
          { new: true }
        )
        .populate({
          path: 'weeklySchedule',
          select: 'name startTime endTime officer forLeader unit hasEquipment',
          populate: {
            path: 'officer',
            select: 'name avatar'
          }
        })
        .populate({
          path: 'weeklyScheduleTemplate.data.unit',
          select: 'name'
        })
        .lean()
        .exec((err, schedule) => {
          if (err) {
            return next(err);
          }

          // Cập nhật status của các DutyShift
          const updateTasks = [];

          // Cập nhật status = 0 cho các shift được thêm (chưa xác nhận)
          if (addedShifts.length > 0) {
            updateTasks.push((callback) => {
              DutyShiftModel.updateMany(
                { _id: { $in: addedShifts } },
                { $set: { status: 1, updatedAt: Date.now() } }
              ).exec(callback);
            });
          }

          // Cập nhật status = 2 cho các shift bị xóa (hủy bỏ)
          if (removedShifts.length > 0) {
            updateTasks.push((callback) => {
              DutyShiftModel.updateMany(
                { _id: { $in: removedShifts } },
                { $set: { status: 2, updatedAt: Date.now() } }
              ).exec(callback);
            });
          }

          // Thực hiện tất cả các cập nhật song song
          async.parallel(updateTasks, (err) => {
            if (err) {
              return next(err);
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật lịch thường trực chiến đấu thành công'
              },
              data: {
                ...schedule,
                updateSummary: {
                  addedShifts: addedShifts.length,
                  removedShifts: removedShifts.length,
                  totalShifts: weeklySchedule.length
                }
              }
            });
          });
        });
      });
  };

  async.waterfall([checkParams, validateTimeConflicts, updateWeeklySchedule], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
