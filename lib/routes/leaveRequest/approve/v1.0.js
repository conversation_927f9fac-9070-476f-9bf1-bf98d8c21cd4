const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const leaveService = require('../../../services/leaveService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API duyệt đơn xin nghỉ phép
 * POST /api/v1.0/leave-request/approve
 */
module.exports = (req, res) => {
  const approverId = req.user.id;
  const {
    requestId,
    status,
    approvalNote
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      requestId: Joi.objectId().required(),
      status: Joi.string().valid('approved', 'rejected').required(),
      approvalNote: Joi.string().max(500).optional().default('')
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const approveRequest = (next) => {
    try {
      leaveService.approveLeaveRequest(
        requestId,
        approverId,
        status,
        approvalNote
      )
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Log hoạt động
    SystemLogModel && SystemLogModel.create({
      user: approverId,
      action: 'approve_leave_request',
      description: `${status === 'approved' ? 'Duyệt' : 'Từ chối'} đơn xin nghỉ phép`,
      data: req.body,
      updatedData: result.data
    }, () => {});
  };

  async.waterfall([
    validateParams,
    approveRequest,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};