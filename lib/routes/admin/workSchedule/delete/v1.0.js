const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const scheduleService = require('../../../../services/scheduleService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API xóa lịch làm việc
 * POST /api/v1.0/work-schedule/delete
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const { scheduleIds } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      scheduleIds: Joi.array().items(Joi.objectId()).min(1).required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const deleteSchedule = (next) => {
    try {
      scheduleService.deleteWorkSchedule(scheduleIds)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Log hoạt động
    SystemLogModel && SystemLogModel.create({
      user: userId,
      action: 'delete_work_schedule',
      description: 'Xóa lịch làm việc',
      data: req.body,
      updatedData: result.data
    }, () => {});
  };

  async.waterfall([
    validateParams,
    deleteSchedule,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};