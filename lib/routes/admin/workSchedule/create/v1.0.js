const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const scheduleService = require('../../../../services/scheduleService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API tạo lịch làm việc cho cán bộ
 * POST /api/v1.0/work-schedule/create
 */
module.exports = (req, res) => {
  const creatorId = req.user.id;
  const {
    schedules // Mảng lịch chi tiết cho từng ngày: [{ date, userIds, shifts }]
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      schedules: Joi.array().items(
        Joi.object({
          // Chỉ chấp nhận định dạng DD-MM-YYYY
          date: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).required(),
          userIds: Joi.array().items(Joi.objectId()).min(1).required(),
          shifts: Joi.array().items(Joi.string().valid('morning', 'afternoon')).min(1).required()
        })
      ).required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số không hợp lệ: ${error.details[0].message}`
        }
      });
    }

    // Kiểm tra duplicate dates trong schedules
    const dateSet = new Set();
    for (const schedule of schedules) {
      if (dateSet.has(schedule.date)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi dữ liệu đầu vào',
            body: `Ngày ${schedule.date} bị trùng lặp trong danh sách lịch. Mỗi ngày chỉ được xuất hiện một lần.`
          }
        });
      }
      dateSet.add(schedule.date);
    }

    // Validate định dạng date cho tất cả schedules
    for (const schedule of schedules) {
      if (!DateUtils.isValidDDMMYYYY(schedule.date)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi định dạng ngày',
            body: `Ngày ${schedule.date} không đúng định dạng DD-MM-YYYY`
          }
        });
      }
    }

    next();
  };

  const createSchedule = (next) => {
    try {
      scheduleService.createFlexibleWorkSchedule(creatorId, schedules)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Log hoạt động
    SystemLogModel && SystemLogModel.create({
      user: creatorId,
      action: 'create_work_schedule',
      description: 'Tạo lịch làm việc',
      data: req.body,
      updatedData: result.data
    }, () => { });
  };

  async.waterfall([
    validateParams,
    createSchedule,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};