const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Area = require('../../../../models/area')
const tool = require('../../../../util/tool')


module.exports = (req, res) => {

  const name = req.body.name || '';
  const level = req.body.level || null; // filter theo cấp độ: 1 = khu vực lớn, 2 = tổ dân phố
  const parent = req.body.parent || null; // filter theo khu vực cha (chỉ áp dụng cho level 2)
  const limit = req.body.limit || 10;
  const page = req.body.page || 1;

  const listAreas = (next) => {

    let objSearch = {
      // status: 1
    }

    if (name && name.trim()) {
      const nameAlias = tool.change_alias(name.trim());

      // Tìm kiếm theo nameAlias (đã được chu<PERSON> hóa)
      objSearch.nameAlias = { $regex: nameAlias };
    }

    // Filter theo cấp độ khu vực
    if (level && [1, 2].includes(parseInt(level))) {
      objSearch.level = parseInt(level);
    }

    // Filter theo khu vực cha (chỉ áp dụng khi tìm tổ dân phố)
    if (parent) {
      objSearch.parent = parent;
    }

    Area
      .find(objSearch)
      .populate('parent', 'name') // populate thông tin khu vực cha
      .sort({ level: 1, createdAt: 1 }) // sắp xếp theo level trước, sau đó theo thời gian tạo
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        });
      })
  }

  async.waterfall([
    listAreas
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}