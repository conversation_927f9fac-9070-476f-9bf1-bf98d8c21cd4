const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const attendanceService = require('../../../services/attendanceService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API điểm danh
 * POST /api/v1.0/attendance/checkin
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    location
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      location: Joi.object({
        lat: Joi.number().optional(),
        lng: Joi.number().optional(),
        address: Joi.string().optional()
      }).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const performCheckin = (next) => {
    try {
      attendanceService.checkin(userId, location)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Log hoạt động
    SystemLogModel && SystemLogModel.create({
      user: userId,
      action: 'checkin_attendance',
      description: 'Điểm danh',
      data: req.body,
      updatedData: result.data
    }, () => {});
  };

  async.waterfall([
    validateParams,
    performCheckin,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};